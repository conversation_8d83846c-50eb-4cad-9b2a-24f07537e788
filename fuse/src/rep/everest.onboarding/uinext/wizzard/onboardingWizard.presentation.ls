package everest.onboarding

odata presentation onboardingWizard {

  data-set Questions {
    supported-operations view
    field id: field<OnboardingQuestion.id>
    field uuid: field<OnboardingQuestion.uuid>
    field text: field<OnboardingQuestion.text>
    field title: field<OnboardingQuestion.title>
    field orderIndex: field<OnboardingQuestion.orderIndex>
  }

  data-set QuestionOptions {
    supported-operations view, change
    field id: field<OnboardingQuestionOption.id>
    field uuid: field<OnboardingQuestionOption.uuid>
    field onboardingQuestionUUID: field<OnboardingQuestionOption.onboardingQuestionUUID>
    field `description`: field<OnboardingQuestionOption.`description`>
    field `value`: field<OnboardingQuestionOption.`value`>
    field orderIndex: field<OnboardingQuestionOption.orderIndex>
    field isSelected: field<OnboardingQuestionOption.isSelected>
  }

  data-set OptionToStepMappings {
    supported-operations view
    field id: field<OnboardingOptionToStepMapping.id>
    field uuid: field<OnboardingOptionToStepMapping.uuid>
    field onboardingOptionUUID: field<OnboardingOptionToStepMapping.onboardingOptionUUID>
    field onboardinStepUUID: field<OnboardingOptionToStepMapping.onboardinStepUUID>
  }

  data-set Steps {
    supported-operations view
    field id: field<OnboardingStep.id>
    field uuid: field<OnboardingStep.uuid>
    field title: field<OnboardingStep.title>
    field orderIndex: field<OnboardingStep.orderIndex>
    field status: field<OnboardingStep.status>
    field primaryLink: field<OnboardingStep.primaryLink>
    field secondaryLink: field<OnboardingStep.secondaryLink>
    field type: field<OnboardingStep.type>
    field isAlwaysVisible: field<OnboardingStep.isAlwaysVisible>
  }

  action InitializeWizard {
    inputs {
      userName: Text
    }
    outputs {
      firstQuestionUuid: UUID
      totalQuestions: Number<Int>
      wizardState: JSON
    }
    properties {
      side-effects true
    }
  }

  action GetCurrentQuestion {
    inputs {
      questionUuid: UUID
    }
    outputs {
      question: JSON
      options: array<JSON>
      canGoBack: TrueFalse
      canGoNext: TrueFalse
      progressInfo: JSON
    }
    properties {
      side-effects false
    }
  }

  action SaveAnswer {
    inputs {
      questionUuid: UUID
      selectedValues: array<Text>
      textAnswer: Text
      userName: Text
    }
    outputs {
      success: TrueFalse
      validationErrors: array<Text>
    }
    properties {
      side-effects true
    }
  }

  action GetNextQuestion {
    inputs {
      currentQuestionUuid: UUID
      selectedAnswers: array<Text>
      userName: Text
    }
    outputs {
      nextQuestionUuid: UUID
      isCompleted: TrueFalse
      routingInfo: JSON
    }
    properties {
      side-effects false
    }
  }

  action GetPreviousQuestion {
    inputs {
      currentQuestionUuid: UUID
      userName: Text
    }
    outputs {
      previousQuestionUuid: UUID
      canGoBack: TrueFalse
    }
    properties {
      side-effects false
    }
  }

  action ValidateAnswer {
    inputs {
      questionUuid: UUID
      selectedValues: array<Text>
      textAnswer: Text
    }
    outputs {
      isValid: TrueFalse
      validationErrors: array<Text>
    }
    properties {
      side-effects false
    }
  }

  action GetProgressInfo {
    inputs {
      currentQuestionUuid: UUID
      userName: Text
    }
    outputs {
      currentStep: Number<Int>
      totalSteps: Number<Int>
      completedSteps: array<Number<Int>>
      progressPercentage: Number<Decimal>
    }
    properties {
      side-effects false
    }
  }

  action ResetWizard {
    inputs {
      userName: Text
    }
    outputs {
      success: TrueFalse
    }
    properties {
      side-effects true
    }
  }

  action SubmitWizard {
    inputs {
      userName: Text
      allAnswers: JSON
    }
    outputs {
      success: TrueFalse
      redirectUrl: Text
      completionMessage: Text
    }
    properties {
      side-effects true
    }
  }

  action GetUserAnswers {
    inputs {
      userName: Text
    }
    outputs {
      answers: JSON
      lastQuestionUuid: UUID
    }
    properties {
      side-effects false
    }
  }
}
